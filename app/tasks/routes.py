from flask import Blueprint, request, jsonify, g
from flask_jwt_extended import jwt_required, get_jwt_identity
from .services import get_all_tasks, create_task, update_task, delete_task
from .github_service import GitHubIssuesService
from app.models.api_response import ApiResponse
from app.history.utils import with_activity_log
from app.history.logger import activity_logger

tasks_bp = Blueprint("tasks_bp", __name__, url_prefix="/tasks")


@tasks_bp.route("/list_tasks", methods=["GET"])
@jwt_required()
def list_tasks():
    current_user_id = get_jwt_identity()
    assignee_id = request.args.get("assignee_id", type=int)
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    # Limit per_page to prevent abuse
    per_page = min(per_page, 100)

    response = get_all_tasks(current_user_id, assignee_id, page, per_page)
    return jsonify(response.to_dict()), response.code


@tasks_bp.route("/create_task_view", methods=["POST"])
@jwt_required()
@with_activity_log(
    action="create_task",
    entity_type="task",
    description_template="Created task {title}"
)
def create_task_view():
    current_user_id = get_jwt_identity()
    data = request.get_json()

    response = create_task(data, current_user_id)
    
    # If task creation was successful, set up activity logging data
    if response.success and response.data:
        task_data = response.data
        g.log_user_id = int(current_user_id)
        g.log_data = {
            "title": task_data.get("title", "Unknown"),
            "entity_id": task_data.get("id"),
            "task_id": task_data.get("id"),
            "project_id": task_data.get("project_id"),
            "status": task_data.get("status"),
            "priority": task_data.get("priority")
        }
    
    return jsonify(response.to_dict()), response.code


@tasks_bp.route("/update_task_view/<int:task_id>", methods=["PUT"])
@jwt_required()
@with_activity_log(
    action="update_task",
    entity_type="task",
    description_template="Updated task {title}"
)
def update_task_view(task_id):
    current_user_id = get_jwt_identity()
    data = request.get_json()
    response = update_task(task_id, data, current_user_id)
    
    # If task update was successful, set up activity logging data
    if response.success and response.data:
        task_data = response.data
        g.log_user_id = int(current_user_id)
        g.log_data = {
            "title": task_data.get("title", "Unknown"),
            "entity_id": task_data.get("id"),
            "task_id": task_data.get("id"),
            "project_id": task_data.get("project_id"),
            "status": task_data.get("status"),
            "priority": task_data.get("priority"),
            "updated_fields": list(data.keys())
        }
    
    return jsonify(response.to_dict()), response.code


@tasks_bp.route("/delete_task_view/<int:task_id>", methods=["DELETE"])
@jwt_required()
def delete_task_view(task_id):
    current_user_id = get_jwt_identity()
    
    # Get task data before deletion for logging
    from app.models.task import Task
    task = Task.query.get(task_id)
    task_title = "Unknown"
    project_id = None
    
    if task:
        task_title = task.title
        project_id = task.project_id
    
    response = delete_task(task_id, current_user_id)
    
    # If deletion was successful, log the activity directly (can't use decorator because task is already gone)
    if response.success:
        activity_logger.log(
            user_id=int(current_user_id),
            action="delete_task",
            entity_type="task",
            description=f"Deleted task {task_title}",
            entity_id=task_id,
            project_id=project_id,
            task_id=None,  # Task is deleted, so don't reference it
            details={
                "title": task_title,
                "project_id": project_id
            }
        )
    
    return jsonify(response.to_dict()), response.code


@tasks_bp.route("/github/sync/<int:project_id>", methods=["POST"])
@jwt_required()
def sync_github_issues(project_id):
    """Sync GitHub issues from a repository to local tasks"""
    try:
        current_user_id = int(get_jwt_identity())
        
        github_service = GitHubIssuesService(current_user_id)
        response = github_service.sync_repository_issues_to_tasks(project_id)

        return jsonify(response.to_dict()), response.code

    except ValueError as e:
        response = ApiResponse.failure(str(e), code=400)
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(f"Error syncing issues: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code
